import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import uploadProductToServerSide from "../../services/products/product-upload";
import { validateProductData } from "../../validation/products/validate-product-data";
import { formDataJsonTransformation } from "../../utils/form-data-json-transformation";
import cleanProductFormData from "../../utils/form-data-cleaning/product-form";
import { SeoMetaContentType } from "@/modules/seo/types";
import { getPreviousUrlQueryParam } from "@/utils/previous-url-params";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";

export default function useProductUpload(
  metaContent: SeoMetaContentType,
  elementId?: string
) {
  const t = useTranslations("warnings");
  const queryClient = useQueryClient();
  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);

  const { toast } = useToast();

  async function submitProduct(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();

    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanProductFormData(formData, elementId);
      validateProductData(filteredFormData);

      setWarning("");

      const submittedData: Record<string, any> =
        formDataJsonTransformation(filteredFormData);

      submittedData["metaContent"] = {
        title: metaContent.title,
        description: metaContent.description,
        tags: metaContent.keywords,
      };

      const res = await uploadProductToServerSide(submittedData, elementId);

      //passing previous url in params to comeback to the products list based on the previous filtering pagination and search
      if (elementId) {
        if (previousUrl && previousUrl.startsWith("/products"))
          router.push(previousUrl);
        else router.push("/products");
      } else {
        if (previousUrl && previousUrl.startsWith("/products"))
          router.push(
            `/products/${res?.slug}/items/creation?${getPreviousUrlQueryParam(
              previousUrl
            )}`
          );
        else router.push(`/products/${res?.slug}/items/creation`);
      }

      queryClient.invalidateQueries({ queryKey: ["product"], exact: false });
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else if (
          customError.message === "A product with this name already exists!"
        ) {
          setWarning(t("upload.productNameTaken"));
          toast({
            title: t("warning"),
            description: t("upload.productNameTaken"),
          });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      } else if (customError.status === 404 && customError.code === "P5000") {
        setWarning(t("upload.categoryNotFound"));
        toast({
          title: t("warning"),
          description: t("upload.categoryNotFound"),
        });
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitProduct,
    formRef,
    warning,
    isPending,
  };
}
