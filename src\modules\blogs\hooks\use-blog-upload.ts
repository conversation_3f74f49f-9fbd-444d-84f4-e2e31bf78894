import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { validateBlogData } from "../validation/blogs-validation";
import { SeoMetaContentType } from "@/modules/seo/types";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import uploadBlogToServerSide from "../services/blog-upload";
import cleanBlogFormData from "../utils/form-data-cleaning/blogs";

export default function useBLogUpload(
  metaContent: SeoMetaContentType,
  elementId?: string
) {
  const t = useTranslations("warnings");

  const queryClient = useQueryClient();
  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);

  const { toast } = useToast();

  async function submitBlog(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      // Add blog ID to form data for edit mode
      if (elementId) {
        formData.append("id", elementId);
      }

      const filteredFormData = cleanBlogFormData(formData, elementId);

      filteredFormData.append(
        "metaContent",
        JSON.stringify({
          title: metaContent.title,
          description: metaContent.description,
          tags: metaContent.keywords,
        })
      );

      validateBlogData(filteredFormData);

      setWarning("");

      await uploadBlogToServerSide(filteredFormData, elementId);

      queryClient.invalidateQueries({
        queryKey: ["blogs"],
        exact: false,
      });

      if (previousUrl && previousUrl.startsWith("/blogs"))
        router.push(previousUrl);
      else router.push("/blogs");
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.code === "P5000") {
          setWarning(t("upload.categoryNotExists"));
          toast({
            title: t("warning"),
            description: t("upload.categoryNotExists"),
          });
        } else if (customError.code === "P9501") {
          setWarning(t("blog.alreadyExists"));
          toast({ title: t("warning"), description: t("blog.alreadyExists") });
        } else if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidData"));
          toast({
            title: t("warning"),
            description: t("upload.invalidData"),
          });
        }
      } else if (customError.status === 404) {
        if (customError.code === "P9000") {
          setWarning(t("upload.tagNotExists"));
          toast({ title: t("warning"), description: t("upload.tagNotExists") });
        } else {
          setWarning(t("upload.notFound"));
          toast({ title: t("warning"), description: t("upload.notFound") });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitBlog,
    formRef,
    warning,
    isPending,
  };
}
