import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { validateCategoryData } from "../../validation/categories/validate-category-data";
import uploadCategoryToServerSide from "../../services/categories/category-upload";
import cleanCategoryFormData from "../../utils/form-data-cleaning/category-form";
import { SeoMetaContentType } from "@/modules/seo/types";

export default function useCategoryUpload({
  metaContent,
  categoryId,
  onSuccess,
}: {
  metaContent: SeoMetaContentType;
  categoryId?: string;
  onSuccess: () => void;
}) {
  const queryClient = useQueryClient();
  const [isPending, setIsPending] = useState(false);
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);
  const t = useTranslations("warnings");
  const [warning, setWarning] = useState("");

  async function submitCategory(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanCategoryFormData(formData);
      validateCategoryData(filteredFormData);

      filteredFormData.append(
        "metaContent",
        JSON.stringify({
          title: metaContent.title,
          description: metaContent.description,
          tags: metaContent.keywords,
        })
      );

      setWarning("");

      await uploadCategoryToServerSide(filteredFormData, categoryId);

      queryClient.invalidateQueries({
        queryKey: ["categories"],
        exact: false,
      });

      if (onSuccess) onSuccess();
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else if (customError.message === "categoryAlreadyExists") {
          setWarning(t("upload.categoryAlreadyExists"));
          toast({
            title: t("warning"),
            description: t("upload.categoryAlreadyExists"),
          });
        } else if (customError.code == "P1000") {
          setWarning(t("upload.displayOrderLessThan1"));
          toast({
            title: t("warning"),
            description: t("upload.displayOrderLessThan1"),
          });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      } else if (customError.status === 404) {
        setWarning(t("upload.categoryNotFound"));
        toast({
          title: t("warning"),
          description: t("upload.categoryNotFound"),
        });
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitCategory,
    formRef,
    warning,
    isPending,
  };
}
