import { useState } from "react";

interface LanguageFormData {
  name: string;
  description: string;
}

interface MultilanguageFormData {
  arabic: LanguageFormData;
  french: LanguageFormData;
  english: LanguageFormData;
}

export default function useMultilanguageForm() {
  const [formData, setFormData] = useState<MultilanguageFormData>({
    arabic: { name: "", description: "" },
    french: { name: "", description: "" },
    english: { name: "", description: "" },
  });

  const updateLanguageData = (
    language: keyof MultilanguageFormData,
    field: keyof LanguageFormData,
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      [language]: {
        ...prev[language],
        [field]: value
      }
    }));
  };

  const getLanguageData = (language: keyof MultilanguageFormData) => {
    return formData[language];
  };

  const populateFormWithData = (formRef: React.RefObject<HTMLFormElement>) => {
    if (!formRef.current) return;

    Object.entries(formData).forEach(([lang, data]) => {
      const nameInput = formRef.current?.querySelector(`[name="name_${lang}"]`) as HTMLInputElement;
      const descInput = formRef.current?.querySelector(`[name="description_${lang}"]`) as HTMLTextAreaElement;
      
      if (nameInput) nameInput.value = data.name;
      if (descInput) descInput.value = data.description;
    });
  };

  return {
    formData,
    updateLanguageData,
    getLanguageData,
    populateFormWithData,
  };
}
