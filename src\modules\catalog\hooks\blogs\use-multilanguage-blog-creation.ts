import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { SeoMetaContentType } from "@/modules/seo/types";
import { validateBlogCreationData } from "../../validation/blogs/validate-blog-creation-data";
import cleanBlogCreationFormData from "../../utils/form-data-cleaning/blog-creation-form";

export default function useMultilanguageBlogCreation(
  metaContent: SeoMetaContentType
) {
  const t = useTranslations("warnings");
  const blogsT = useTranslations("BlogsManagement");

  const queryClient = useQueryClient();
  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState("french");

  const { toast } = useToast();

  const handleLanguageChange = (language: string) => {
    setActiveLanguage(language);
  };

  async function submitBlog(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanBlogCreationFormData(formData);
      validateBlogCreationData(filteredFormData);

      const metaContentData = getMetaContent();
      filteredFormData.append(
        "metaContent",
        JSON.stringify(metaContentData.content)
      );

      setWarning("");

      // const response = await uploadBlogToServerSide(submittedData);

      queryClient.invalidateQueries({
        queryKey: ["blogs"],
        exact: false,
      });
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitBlog,
    formRef,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
  };
}
