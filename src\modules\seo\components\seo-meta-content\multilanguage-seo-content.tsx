import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import LanguageTabs from "@/modules/catalog/components/brands/brand-upload/language-tabs";
import MultilanguageMetaFields from "./multilanguage-meta-fields";
import KeywordsSection from "./keywords";
import { Language } from "../../types/multilanguage-seo";

interface MultilanguageSeoContentProps {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  activeLanguage: Language;
  handleLanguageChange: (value: string) => void;
  changeMetaTitle: (title: string) => void;
  changeMetaDescription: (description: string) => void;
  addNewKeyword: (keyword: string) => void;
  removeKeyword: (keyword: string) => void;
}

export default function MultilanguageSeoContent({
  metaTitle,
  metaDescription,
  keywords,
  activeLanguage,
  handleLanguageChange,
  changeMetaTitle,
  changeMetaDescription,
  addNewKeyword,
  removeKeyword,
}: MultilanguageSeoContentProps) {
  const uploadContent = useTranslations(
    "shared.forms.upload.seoLabels.seoSettings"
  );
  const t = useTranslations("shared.forms.upload");

  const languageOptions: { key: Language; value: string }[] = [
    { key: "arabic", value: t("languages.arabic") },
    { key: "french", value: t("languages.french") },
    { key: "english", value: t("languages.english") },
  ];

  return (
    <div className="space-y-7">
      <Text textStyle="TS5" className="font-bold text-black">
        {uploadContent("title")}
      </Text>

      <div className="space-y-5">
        <LanguageTabs
          options={languageOptions}
          onSelect={handleLanguageChange}
          selectedValue={activeLanguage}
        />

        <div
          style={{
            display: activeLanguage === "arabic" ? "block" : "none",
          }}
        >
          <div className="space-y-5">
            <KeywordsSection
              keywords={keywords}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
            />
            <MultilanguageMetaFields
              multilanguage={true}
              activeLanguage="arabic"
              metaTitle={metaTitle}
              metaDescription={metaDescription}
              onTitleChange={changeMetaTitle}
              onDescriptionChange={changeMetaDescription}
            />
          </div>
        </div>

        <div
          style={{
            display: activeLanguage === "french" ? "block" : "none",
          }}
        >
          <div className="space-y-5">
            <KeywordsSection
              keywords={keywords}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
            />
            <MultilanguageMetaFields
              multilanguage={true}
              activeLanguage="french"
              metaTitle={metaTitle}
              metaDescription={metaDescription}
              onTitleChange={changeMetaTitle}
              onDescriptionChange={changeMetaDescription}
            />
          </div>
        </div>

        <div
          style={{
            display: activeLanguage === "english" ? "block" : "none",
          }}
        >
          <div className="space-y-5">
            <KeywordsSection
              keywords={keywords}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
            />
            <MultilanguageMetaFields
              multilanguage={true}
              activeLanguage="english"
              metaTitle={metaTitle}
              metaDescription={metaDescription}
              onTitleChange={changeMetaTitle}
              onDescriptionChange={changeMetaDescription}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
